package com.seer.trick.fleet.service

import com.seer.trick.fleet.domain.MapPath
import com.seer.trick.fleet.domain.SceneContainerType
import com.seer.trick.fleet.domain.normalizeRadian
import com.seer.trick.fleet.traffic.venus.State
import com.seer.trick.fleet.traffic.venus.VenusHelper
import com.seer.trick.fleet.traffic.venus.cache.DijkstraCache
import kotlin.math.PI

class NarrowSideAhead {
  /**
   * 计算容器的窄边方向偏移角度
   */
  private fun calculateContainerNarrowSideOffset(containerType: SceneContainerType): Double {
    return when {
      // 长宽相等：正方体，不区别窄边或者宽边，使用 0 度偏移
      kotlin.math.abs(containerType.outerLength - containerType.outerWidth) < 0.01 -> 0.0
      // 长 > 宽：窄边方向为垂直于长度的方向，需要旋转 90° 使窄边朝前
      containerType.outerLength > containerType.outerWidth -> PI / 2
      // 宽 > 长：窄边就是长度方向，不需要旋转，0°
      else -> 0.0
    }
  }

  /**
   * 寻找最佳换向点
   */
  private fun findBestRotationPoint(fromPointName: String, toPointName: String): String? {
    // 首先尝试向前查找换向点（优先选择距离目标较近的点）
    val forwardRotationPoint = findRotationPointForward(fromPointName, toPointName)
    if (forwardRotationPoint != null) {
      if (logLow) {
        nodeLogs += VenusHelper.logTimestamp() +
          "找到向前换向点：从$fromPointName$toPointName，换向点=$forwardRotationPoint"
      }
      return forwardRotationPoint
    }

    // 如果向前找不到，向后查找
    val backwardRotationPoint = findRotationPointBackward(fromPointName, toPointName)
    if (backwardRotationPoint != null) {
      if (logLow) {
        nodeLogs += VenusHelper.logTimestamp() +
          "找到向后换向点：从$fromPointName$toPointName，换向点=$backwardRotationPoint"
      }
      return backwardRotationPoint
    }

    // 未找到任何换向点
    if (logLow) {
      nodeLogs += VenusHelper.logTimestamp() +
        "未找到换向点：从$fromPointName$toPointName"
    }

    return null
  }

  /**
   * 检查当前状态是否需要在换向点进行容器旋转
   */
  private fun shouldRotateAtRotationPoint(fromState: State, path: MapPath): Boolean {
    // 检查是否需要窄边通行
    if (!shouldPerformNarrowSideAhead(path, fromState)) return false

    // 检查当前是否在换向点
    val currentPointName = fromState.toPosition.pointName ?: return false
    val pointRecord = areaMapCache.pointNameMap[currentPointName] ?: return false

    return pointRecord.point.containerRotateAllowed
  }

  /**
   * 向前查找换向点（优先选择距离目标较近的点）
   */
  private fun findRotationPointForward(fromPointName: String, toPointName: String): String? {
    val rr = sr.mustGetRobot(robotName)
    val shortestPath = DijkstraCache.getPathOrCompute(rr, areaMapCache.areaId, fromPointName, toPointName)

    if (shortestPath.isNullOrEmpty()) return null

    // 从起点向目标点方向查找换向点，优先选择距离目标较近的点
    for (pointName in shortestPath) {
      val pointRecord = areaMapCache.pointNameMap[pointName]
      if (pointRecord != null && pointRecord.point.containerRotateAllowed) {
        return pointName
      }
    }

    return null
  }


  /**
   * 计算从当前朝向到目标朝向的最小角度差
   */
  private fun calculateOptimalRotationAngle(currentTheta: Double, targetTheta: Double): Double {
    var angleDiff = (targetTheta - currentTheta).normalizeRadian()

    // 如果角度差超过半圆（π），选择反向旋转
    if (kotlin.math.abs(angleDiff) > PI) {
      angleDiff = if (angleDiff > 0) {
        angleDiff - 2 * PI
      } else {
        angleDiff + 2 * PI
      }
    }

    return angleDiff
  }

  /**
   * 检查是否需要进行窄边通行处理
   */
  private fun  shouldPerformNarrowSideAhead(path: MapPath, fromState: State): Boolean {
    // 1. 检查路径是否设置了窄边通行
    if (!path.containerShortSideAhead) return false

    // 2. 检查机器人是否有容器
    val cm = collisionModels[robotName] ?: return false
    if (!cm.loaded) return false

    // 3. 检查容器是否支持旋转（机器人必须支持料架在机器人身上旋转）
    val rr = sr.mustGetRobot(robotName)
    val containerType = rr.sr.containerTypes.values.find { it.name == cm.containerTypeName }
    if (containerType == null) return false

    // 4. 检查机器人组是否支持容器旋转（salverNotRotate为false表示支持旋转）
    val group = rr.mustGetGroup()
    if (group.salverNotRotate) return false

    // 5. 检查容器是否可独立旋转（loadRotatable为true表示货物能旋转）
    if (!cm.loadRotatable) return false

    return true
  }

  /**
   * 货物朝向，不涉及机器人朝向的计算
   */
  private fun adjustLoadThetaForNarrowSideAhead(
    originalLoadEnterTheta: Double,
    originalLoadExitTheta: Double,
    pathEnterTheta: Double, // 路径正向进入角度
    pathExitTheta: Double   // 路径正向退出角度
  ): Pair<Double, Double> {
    // 获取容器类型信息
    val rr = sr.mustGetRobot(robotName)
    val cm = collisionModels[robotName]!!
    val containerType = rr.sr.containerTypes.values.find { it.name == cm.containerTypeName }
      ?: return originalLoadEnterTheta to originalLoadExitTheta // 安全回退

    // 计算容器窄边偏移角度
    val narrowSideOffset = calculateContainerNarrowSideOffset(containerType)

    // 定义计算目标货物朝向的核心逻辑
    fun calculateOptimalLoadTheta(currentLoadTheta: Double, pathTheta: Double): Double {
      // 计算窄边通行所需的目标朝向：路径方向 + 窄边偏移
      val targetTheta = (pathTheta + narrowSideOffset).normalizeRadian()

      // 计算当前朝向到目标朝向的角度差
      val angleDiff = calculateOptimalRotationAngle(currentLoadTheta, targetTheta)

      // 如果角度差很小（小于5度），认为已经满足窄边要求，不需要调整
      val tolerance = Math.toRadians(5.0)
      if (kotlin.math.abs(angleDiff) < tolerance) {
        return currentLoadTheta
      }

      // 否则返回目标朝向
      return targetTheta
    }

    // 分别计算进入和退出时的最优货物朝向
    val adjustedLoadEnterTheta = calculateOptimalLoadTheta(originalLoadEnterTheta, pathEnterTheta)
    val adjustedLoadExitTheta = calculateOptimalLoadTheta(originalLoadExitTheta, pathExitTheta)

    return adjustedLoadEnterTheta to adjustedLoadExitTheta
  }

  /**
   * 获取机器人与货物之间的固定偏移角度
   */
  private fun getLoadToRobotOffset(robotName: String): Double {
    val cm = collisionModels[robotName] ?: return 0.0
    return if (cm.loaded) {
      // 使用配置中的货物初始角度作为偏移角
      cm.loadInitTheta
    } else {
      0.0
    }
  }
}